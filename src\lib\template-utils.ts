// Client-side utility functions for templates (no Node.js dependencies)

// Extract placeholders from HTML content
export function extractPlaceholders(htmlContent: string): string[] {
  const placeholderRegex = /\[([^\]]+)\]/g;
  const placeholders = new Set<string>();
  let match;

  while ((match = placeholderRegex.exec(htmlContent)) !== null) {
    placeholders.add(match[0]); // Include the brackets
  }

  return Array.from(placeholders).sort();
}

// Count images that need to be loaded from HTML content
export function countImagesInHtml(htmlContent: string): number {
  // Match img tags with src attributes that are not empty and not data URLs
  const imgRegex = /<img[^>]+src\s*=\s*["']([^"']+)["'][^>]*>/gi;
  const images = [];
  let match;

  while ((match = imgRegex.exec(htmlContent)) !== null) {
    const src = match[1];
    // Skip data URLs, empty sources, and already absolute URLs
    if (src && !src.startsWith('data:') && !src.startsWith('http') && !src.startsWith('/')) {
      images.push(src);
    }
  }

  return images.length;
}

// Extract image sources from HTML content
export function extractImageSources(htmlContent: string): string[] {
  const imgRegex = /<img[^>]+src\s*=\s*["']([^"']+)["'][^>]*>/gi;
  const images = [];
  let match;

  while ((match = imgRegex.exec(htmlContent)) !== null) {
    const src = match[1];
    // Skip data URLs, empty sources, and already absolute URLs
    if (src && !src.startsWith('data:') && !src.startsWith('http') && !src.startsWith('/')) {
      images.push(src);
    }
  }

  return images;
}

// Update image paths in HTML content to point to uploaded folder
export function updateImagePaths(htmlContent: string, templateId: string): string {
  return htmlContent.replace(
    /<img([^>]+)src\s*=\s*["']([^"']+)["']([^>]*>)/gi,
    (match, beforeSrc, src, afterSrc) => {
      // Skip data URLs, empty sources, and already absolute URLs
      if (!src || src.startsWith('data:') || src.startsWith('http') || src.startsWith('/')) {
        return match;
      }
      
      // Update the src to point to the uploaded folder
      const newSrc = `/${templateId}/${src}`;
      return `<img${beforeSrc}src="${newSrc}"${afterSrc}`;
    }
  );
}

// Clean Word HTML content (client-side version without complex cleaning)
export function cleanWordHtmlBasic(htmlContent: string): string {
  let cleaned = htmlContent;

  // Remove Word-specific XML namespaces and tags
  cleaned = cleaned.replace(/<\?xml[^>]*>/gi, '');
  cleaned = cleaned.replace(/<\/?o:[^>]*>/gi, '');
  cleaned = cleaned.replace(/<\/?w:[^>]*>/gi, '');
  cleaned = cleaned.replace(/<\/?m:[^>]*>/gi, '');
  cleaned = cleaned.replace(/<\/?v:[^>]*>/gi, '');

  // Remove Word-specific attributes
  cleaned = cleaned.replace(/\s*mso-[^=]*="[^"]*"/gi, '');
  cleaned = cleaned.replace(/\s*class="Mso[^"]*"/gi, '');

  // Clean up extra whitespace
  cleaned = cleaned.replace(/\n\s*\n\s*\n/g, '\n\n');

  return cleaned.trim();
}

// Generate a unique filename for a template
export function generateTemplateFilename(name: string): string {
  const slug = name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');

  return `${slug}.html`;
}
