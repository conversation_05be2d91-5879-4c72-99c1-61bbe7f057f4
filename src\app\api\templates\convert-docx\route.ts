import { NextRequest, NextResponse } from 'next/server';
import mammoth from 'mammoth';
import { cleanMammothHtml } from '@/lib/templates';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.docx')) {
      return NextResponse.json(
        { error: 'Only DOCX files are supported' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Convert DOCX to HTML using mammoth
    const result = await mammoth.convertToHtml({ buffer });

    // Get the HTML content and clean it for template use
    const rawHtmlContent = result.value;
    const htmlContent = cleanMammothHtml(rawHtmlContent);
    
    // Log any conversion warnings (optional)
    if (result.messages.length > 0) {
      console.log('Mammoth conversion messages:', result.messages);
    }

    // Return the HTML content
    return NextResponse.json({
      success: true,
      htmlContent,
      originalFilename: file.name,
      messages: result.messages
    });

  } catch (error) {
    console.error('Error converting DOCX:', error);
    return NextResponse.json(
      { error: 'Failed to convert DOCX file' },
      { status: 500 }
    );
  }
}
