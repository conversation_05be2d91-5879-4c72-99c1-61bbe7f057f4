import { NextRequest, NextResponse } from 'next/server';
import { loadTemplates, updateTemplate, loadTemplateFile } from '@/lib/templates';
import { extractPlaceholders } from '@/lib/template-utils';

export async function POST(request: NextRequest) {
  try {
    // Load all templates
    const templates = await loadTemplates();
    const updatedTemplates = [];

    for (const template of templates) {
      try {
        // Load the template content
        const content = await loadTemplateFile(template.filename);
        
        // Re-extract placeholders with the new normalization logic
        const newPlaceholders = extractPlaceholders(content);
        
        // Only update if placeholders have changed
        if (JSON.stringify(template.placeholders) !== JSON.stringify(newPlaceholders)) {
          await updateTemplate(template.id, {
            placeholders: newPlaceholders
          });
          
          updatedTemplates.push({
            id: template.id,
            name: template.name,
            oldCount: template.placeholders.length,
            newCount: newPlaceholders.length,
            oldPlaceholders: template.placeholders,
            newPlaceholders: newPlaceholders
          });
        }
      } catch (error) {
        console.error(`Error processing template ${template.id}:`, error);
      }
    }

    return NextResponse.json({
      success: true,
      message: `Fixed placeholders for ${updatedTemplates.length} templates`,
      updatedTemplates
    });
  } catch (error) {
    console.error('Error fixing placeholders:', error);
    return NextResponse.json(
      { error: 'Failed to fix placeholders' },
      { status: 500 }
    );
  }
}
