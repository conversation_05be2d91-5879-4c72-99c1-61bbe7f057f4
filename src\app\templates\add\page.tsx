"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Upload, FileText, AlertCircle, Folder } from "lucide-react";
import { toast } from "sonner";
import { countImagesInHtml } from "@/lib/template-utils";

export default function AddTemplatePage() {
  const router = useRouter();
  const [isUploading, setIsUploading] = useState(false);
  const [isConverting, setIsConverting] = useState(false);
  const [imageCount, setImageCount] = useState(0);
  const [showFolderUpload, setShowFolderUpload] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    layoutSize: "A4" as "A4" | "Letter",
    file: null as File | null,
    folderFiles: [] as File[],
  });

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type - only .htm files allowed
      const fileName = file.name.toLowerCase();
      if (!fileName.endsWith(".htm")) {
        toast.error("Please select an HTM file (.htm)");
        return;
      }

      // Read file content to check for images
      try {
        const htmlContent = await file.text();
        const imageCount = countImagesInHtml(htmlContent);

        setImageCount(imageCount);
        setShowFolderUpload(imageCount >= 2);

        if (imageCount >= 2) {
          toast.info(
            `Detected ${imageCount} images. Please upload the folder containing these images.`
          );
        }
      } catch (error) {
        console.error("Error reading file:", error);
        toast.error("Error reading file content");
        return;
      }

      setFormData((prev) => ({ ...prev, file }));
    }
  };

  const handleFolderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setFormData((prev) => ({ ...prev, folderFiles: files }));

    if (files.length > 0) {
      toast.success(`Selected ${files.length} files from folder`);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error("Please enter a template name");
      return;
    }

    if (!formData.description.trim()) {
      toast.error("Please enter a template description");
      return;
    }

    if (!formData.file) {
      toast.error("Please select an HTM file");
      return;
    }

    if (showFolderUpload && formData.folderFiles.length === 0) {
      toast.error("Please upload the folder containing the images");
      return;
    }

    setIsUploading(true);

    try {
      // For HTM files, read content directly
      const htmlContent = await formData.file.text();

      // First, create the template
      const templateResponse = await fetch("/api/templates", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          description: formData.description.trim(),
          layoutSize: formData.layoutSize,
          htmlContent: htmlContent,
          hasFolder: showFolderUpload && formData.folderFiles.length > 0,
        }),
      });

      if (!templateResponse.ok) {
        const error = await templateResponse.json();
        throw new Error(error.message || "Failed to upload template");
      }

      const templateResult = await templateResponse.json();
      const templateId = templateResult.template.id;

      // If folder files are provided, upload them
      if (showFolderUpload && formData.folderFiles.length > 0) {
        toast.info("Uploading folder files...");

        const folderFormData = new FormData();
        folderFormData.append("templateId", templateId);

        formData.folderFiles.forEach((file) => {
          folderFormData.append("files", file);
        });

        const folderResponse = await fetch("/api/templates/upload-folder", {
          method: "POST",
          body: folderFormData,
        });

        if (!folderResponse.ok) {
          const error = await folderResponse.json();
          throw new Error(error.message || "Failed to upload folder");
        }

        toast.success("Folder uploaded successfully!");
      }

      toast.success("Template uploaded successfully!");
      router.push("/");
    } catch (error) {
      console.error("Upload error:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to upload template"
      );
    } finally {
      setIsUploading(false);
      setIsConverting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <div className="mb-8">
        <h1 className="text-3xl font-semibold text-foreground mb-2">
          Add New Template
        </h1>
        <p className="text-muted-foreground">
          Upload an HTM template file to create a new document template.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Template Information
          </CardTitle>
          <CardDescription>
            Provide details about your template and upload the DOCX or HTML
            file.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">Template Name</Label>
              <Input
                id="name"
                type="text"
                placeholder="e.g., Certificate of Good Moral"
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                type="text"
                placeholder="e.g., Certificate of good moral character"
                value={formData.description}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="layoutSize">Layout Size</Label>
              <Select
                value={formData.layoutSize}
                onValueChange={(value: "A4" | "Letter") =>
                  setFormData((prev) => ({ ...prev, layoutSize: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select layout size" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="A4">A4 (210 × 297 mm)</SelectItem>
                  <SelectItem value="Letter">Letter (8.5 × 11 in)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="file">Template File</Label>
              <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                <input
                  id="file"
                  type="file"
                  accept=".htm"
                  onChange={handleFileChange}
                  className="hidden"
                  required
                />
                <label
                  htmlFor="file"
                  className="cursor-pointer flex flex-col items-center gap-2"
                >
                  <Upload className="h-8 w-8 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">
                      {formData.file
                        ? formData.file.name
                        : "Click to upload HTM file"}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Supports .htm files only
                    </p>
                  </div>
                </label>
              </div>
            </div>

            {showFolderUpload && (
              <div className="space-y-2">
                <Label htmlFor="folder">Image Folder</Label>
                <div className="border-2 border-dashed border-orange-300 bg-orange-50 dark:bg-orange-950/20 rounded-lg p-6 text-center">
                  <input
                    id="folder"
                    type="file"
                    multiple
                    onChange={handleFolderChange}
                    className="hidden"
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    {...({ webkitdirectory: "" } as any)}
                  />
                  <label
                    htmlFor="folder"
                    className="cursor-pointer flex flex-col items-center gap-2"
                  >
                    <Folder className="h-8 w-8 text-orange-600" />
                    <div>
                      <p className="text-sm font-medium text-orange-800">
                        {formData.folderFiles.length > 0
                          ? `${formData.folderFiles.length} files selected`
                          : "Click to upload image folder"}
                      </p>
                      <p className="text-xs text-orange-600">
                        Upload the folder containing {imageCount} images
                        detected in your HTM file
                      </p>
                    </div>
                  </label>
                </div>
              </div>
            )}

            <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium text-blue-800 dark:text-blue-200 mb-1">
                    Template Guidelines:
                  </p>
                  <ul className="text-blue-700 dark:text-blue-300 space-y-1 list-disc list-inside">
                    <li>
                      Upload HTM files only (save Word documents as &quot;Web
                      Page, Filtered&quot; .htm format)
                    </li>
                    <li>
                      Use placeholders like [name], [date], [address] for
                      dynamic content
                    </li>
                    <li>
                      If your HTM file contains 2+ images, upload the folder
                      with all images
                    </li>
                    <li>
                      The system will automatically clean Word-specific
                      formatting
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                disabled={isUploading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isUploading} className="flex-1">
                {isConverting
                  ? "Converting DOCX..."
                  : isUploading
                  ? "Uploading..."
                  : "Upload Template"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
